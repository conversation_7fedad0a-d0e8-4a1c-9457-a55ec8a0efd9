'use client';
import { useDarkMode } from '@/contexts/DarkModeContext';
import { CustomImage } from '@/src/components/common';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import { goalsService, valuesAPI } from '@/src/lib/api';
import { useUserProfile } from '@/src/lib/hooks';
import {
	useMyLifeMissions,
	useDeleteLifeMission,
} from '@/src/lib/hooks/useLifeMissions';
import React, { useEffect, useState } from 'react';
import {
	FaUserEdit,
	FaBell,
	FaShieldAlt,
	FaUserPlus,
	FaTrash,
	FaEye,
	FaEyeSlash,
	FaSpinner,
	FaExclamationTriangle,
} from 'react-icons/fa';

const Profile = () => {
	const [values, setValues] = useState([]);
	const [valueLoading, setValueLoading] = useState(true);
	const [valueError, setValueError] = useState(null);
	const [goals, setGoals] = useState([]);
	const [goalError, setGoalError] = useState(false);
	const [goalLoading, setGoalLoading] = useState(true);
	const [deletingMissions, setDeletingMissions] = useState(new Set());
	const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);

	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
	} = useUserProfile();

	const {
		data: userLifeMission,
		isLoading: isLifeMissionLoading,
		error: lifeMissionError,
		refetch: refetchLifeMissions,
	} = useMyLifeMissions();

	const deleteLifeMissionMutation = useDeleteLifeMission();

	useEffect(() => {
		const loadValues = async () => {
			try {
				setValueLoading(true);
				const response = await valuesAPI.getUserValues();
				setValues(response.data.values);
				setValueError(null);
			} catch (err) {
				console.error('Failed to load values:', err);
				setValueError('Failed to load values. Please try again.');
			} finally {
				setValueLoading(false);
			}
		};

		const loadGoals = async () => {
			try {
				setGoalLoading(true);
				const response = await goalsService.getUserGoals();
				setGoals(response.data.goals);
				setGoalError(null);
			} catch (err) {
				console.error('Failed to load goals:', err);
				setGoalError('Failed to load goals. Please try again.');
			} finally {
				setGoalLoading(false);
			}
		};

		loadGoals();
		loadValues();
	}, []);

	const [isSidebarOpen, setIsSidebarOpen] = useState(false);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	const handleDeleteMission = async (missionId) => {
		setDeletingMissions((prev) => new Set(prev).add(missionId));
		setShowDeleteConfirm(null);

		try {
			await deleteLifeMissionMutation.mutateAsync(missionId);
			// Refetch the missions after successful deletion
			await refetchLifeMissions();
		} catch (error) {
			console.error('Failed to delete mission:', error);
			// You might want to show a toast notification here
		} finally {
			setDeletingMissions((prev) => {
				const newSet = new Set(prev);
				newSet.delete(missionId);
				return newSet;
			});
		}
	};

	const confirmDelete = (missionId) => {
		setShowDeleteConfirm(missionId);
	};

	const cancelDelete = () => {
		setShowDeleteConfirm(null);
	};

	// Loading skeleton component
	const LoadingSkeleton = ({
		className = 'h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse',
	}) => <div className={className}></div>;

	// Profile header loading state
	if (isUserLoading) {
		return (
			<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
				<FixedHeader
					title='Profile'
					toggleSidebar={toggleSidebar}
				/>
				<div className='container mx-auto px-4 py-8 pt-20'>
					<div className='flex flex-col items-center mb-8'>
						<div className='w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse'></div>
						<div className='mt-4 h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse'></div>
					</div>
					<div className='space-y-6'>
						{[...Array(4)].map((_, i) => (
							<div
								key={i}
								className='rounded-lg shadow-md bg-white dark:bg-gray-800 p-6'>
								<div className='h-5 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4'></div>
								<div className='space-y-2'>
									<div className='h-4 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse'></div>
									<div className='h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse'></div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
			<FixedHeader
				title='Profile'
				toggleSidebar={toggleSidebar}
			/>
			<div className='container mx-auto px-4 py-8 pt-20'>
				{/* Profile Header */}
				<div className='flex flex-col items-center mb-8'>
					<div className='w-24 h-24 rounded-full overflow-hidden shadow-lg ring-4 ring-white dark:ring-gray-800 hover:ring-teal-200 dark:hover:ring-teal-800 transition-all duration-200 cursor-pointer relative'>
						<CustomImage
							src={userProfile?.user?.avatar}
							alt='User Avatar'
							className='w-full h-full object-cover hover:scale-110 transition-transform duration-200'
						/>
						<div className='absolute bottom-0 right-0 bg-teal-500 rounded-full p-1 border-2 border-white dark:border-gray-900'>
							<FaUserEdit className='text-white text-xs' />
						</div>
					</div>
					<p className='mt-4 text-lg font-semibold text-gray-800 dark:text-gray-200'>
						{userProfile?.user?.name || 'User Name'}
					</p>
				</div>

				{/* Profile Content */}
				<div className='space-y-6'>
					{/* Email and About Section */}
					<div className='rounded-lg shadow-md overflow-hidden bg-white dark:bg-gray-800 transition-colors duration-300 p-6'>
						<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4'>
							Contact Information
						</h3>
						<div className='space-y-3'>
							<div>
								<label className='text-sm text-gray-600 dark:text-gray-400'>
									Email
								</label>
								<p className='text-gray-800 dark:text-gray-200'>
									{userProfile?.user?.email || 'Not provided'}
								</p>
							</div>
							<div>
								<label className='text-sm text-gray-600 dark:text-gray-400'>
									About
								</label>
								<p className='text-gray-800 dark:text-gray-200'>
									{userProfile?.user?.about || 'No description available'}
								</p>
							</div>
						</div>
					</div>

					{/* Life Missions Section */}
					<div className='rounded-lg shadow-md overflow-hidden bg-white dark:bg-gray-800 transition-colors duration-300 p-6'>
						<div className='flex items-center justify-between mb-4'>
							<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200'>
								Life Missions
							</h3>
							{isLifeMissionLoading && (
								<FaSpinner className='animate-spin text-teal-500' />
							)}
						</div>

						{isLifeMissionLoading ? (
							<div className='space-y-4'>
								{[...Array(2)].map((_, i) => (
									<div
										key={i}
										className='bg-gray-50 dark:bg-gray-700 p-4 rounded-lg'>
										<LoadingSkeleton className='h-5 w-3/4 mb-2' />
										<LoadingSkeleton className='h-3 w-1/2 mb-2' />
										<LoadingSkeleton className='h-3 w-1/4' />
									</div>
								))}
							</div>
						) : lifeMissionError ? (
							<div className='text-center py-8'>
								<FaExclamationTriangle className='text-red-500 text-2xl mx-auto mb-2' />
								<p className='text-red-500 dark:text-red-400'>
									Failed to load life missions. Please try again.
								</p>
								<button
									onClick={() => refetchLifeMissions()}
									className='mt-2 px-4 py-2 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors'>
									Retry
								</button>
							</div>
						) : userLifeMission?.data && userLifeMission.data.length > 0 ? (
							<div className='space-y-4'>
								{userLifeMission.data.map((mission, index) => (
									<div
										key={mission._id}
										className={`p-4 rounded-lg border-l-4 ${
											index === 0
												? 'bg-teal-50 dark:bg-teal-900/20 border-teal-500'
												: 'bg-gray-50 dark:bg-gray-700 border-gray-400'
										}`}>
										<div className='flex items-start justify-between'>
											<div className='flex-1'>
												<div className='flex items-center gap-2 mb-2'>
													{index === 0 && (
														<span className='bg-teal-500 text-white text-xs px-2 py-1 rounded-full font-medium'>
															Primary
														</span>
													)}
													{mission.isPublic ? (
														<span className='flex items-center text-xs text-teal-600 dark:text-teal-400 bg-teal-100 dark:bg-teal-900/30 px-2 py-1 rounded-full'>
															<FaEye className='mr-1' /> Public
														</span>
													) : (
														<span className='flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full'>
															<FaEyeSlash className='mr-1' /> Private
														</span>
													)}
												</div>
												<p className='text-gray-800 dark:text-gray-200 font-medium mb-2'>
													{mission.mission}
												</p>
												<span className='text-xs text-gray-500 dark:text-gray-400'>
													Created:{' '}
													{new Date(mission.createdAt).toLocaleDateString()}
												</span>
											</div>

											<div className='ml-4'>
												{showDeleteConfirm === mission._id ? (
													<div className='flex items-center gap-2'>
														<span className='text-xs text-gray-600 dark:text-gray-400 mr-2'>
															Delete this mission?
														</span>
														<button
															onClick={() => handleDeleteMission(mission._id)}
															disabled={deletingMissions.has(mission._id)}
															className='bg-red-500 text-white px-3 py-1 rounded text-xs hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
															{deletingMissions.has(mission._id) ? (
																<FaSpinner className='animate-spin' />
															) : (
																'Yes'
															)}
														</button>
														<button
															onClick={cancelDelete}
															className='bg-gray-500 text-white px-3 py-1 rounded text-xs hover:bg-gray-600 transition-colors'>
															Cancel
														</button>
													</div>
												) : (
													<button
														onClick={() => confirmDelete(mission._id)}
														disabled={deletingMissions.has(mission._id)}
														className='text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-2 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
														<FaTrash className='text-sm' />
													</button>
												)}
											</div>
										</div>
									</div>
								))}
							</div>
						) : (
							<div className='text-center py-8'>
								<p className='text-gray-500 dark:text-gray-400'>
									No life missions added yet
								</p>
							</div>
						)}
					</div>

					{/* Values Section */}
					<div className='rounded-lg shadow-md overflow-hidden bg-white dark:bg-gray-800 transition-colors duration-300 p-6'>
						<div className='flex items-center justify-between mb-4'>
							<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200'>
								Values
							</h3>
							{valueLoading && (
								<FaSpinner className='animate-spin text-teal-500' />
							)}
						</div>

						{valueLoading ? (
							<div className='flex space-x-3'>
								{[...Array(5)].map((_, i) => (
									<LoadingSkeleton
										key={i}
										className='h-10 bg-gray-200 dark:bg-gray-700 rounded-full w-24'
									/>
								))}
							</div>
						) : valueError ? (
							<div className='text-center py-4'>
								<FaExclamationTriangle className='text-red-500 text-xl mx-auto mb-2' />
								<p className='text-red-500 dark:text-red-400 text-sm'>
									{valueError}
								</p>
							</div>
						) : values.length > 0 ? (
							<div className='flex flex-wrap gap-3'>
								{values.slice(0, 5).map((value) => (
									<div
										key={value._id}
										className='bg-teal-100 dark:bg-teal-900/30 text-teal-800 dark:text-teal-200 px-4 py-2 rounded-full text-sm font-medium border border-teal-200 dark:border-teal-700'>
										{value.name}
									</div>
								))}
								{values.length > 5 && (
									<div className='bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-4 py-2 rounded-full text-sm font-medium'>
										+{values.length - 5} more
									</div>
								)}
							</div>
						) : (
							<p className='text-gray-500 dark:text-gray-400'>
								No values added yet
							</p>
						)}
					</div>

					{/* Goals Section */}
					<div className='rounded-lg shadow-md overflow-hidden bg-white dark:bg-gray-800 transition-colors duration-300 p-6'>
						<div className='flex items-center justify-between mb-4'>
							<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200'>
								Primary Goal
							</h3>
							{goalLoading && (
								<FaSpinner className='animate-spin text-teal-500' />
							)}
						</div>

						{goalLoading ? (
							<div className='bg-gray-50 dark:bg-gray-700 p-4 rounded-lg'>
								<LoadingSkeleton className='h-6 w-2/3 mb-3' />
								<LoadingSkeleton className='h-2 w-full mb-2' />
								<LoadingSkeleton className='h-3 w-1/3' />
							</div>
						) : goalError ? (
							<div className='text-center py-4'>
								<FaExclamationTriangle className='text-red-500 text-xl mx-auto mb-2' />
								<p className='text-red-500 dark:text-red-400 text-sm'>
									Failed to load goals. Please try again.
								</p>
							</div>
						) : goals.length > 0 ? (
							<div className='bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg border-l-4 border-teal-500'>
								<div className='flex items-center gap-2 mb-3'>
									<h4 className='text-lg font-medium text-gray-800 dark:text-gray-200'>
										{goals[0].title}
									</h4>
									{goals[0].isPublic ? (
										<span className='flex items-center text-xs text-teal-600 dark:text-teal-400 bg-teal-100 dark:bg-teal-900/30 px-2 py-1 rounded-full'>
											<FaEye className='mr-1' /> Public
										</span>
									) : (
										<span className='flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full'>
											<FaEyeSlash className='mr-1' /> Private
										</span>
									)}
								</div>

								{goals[0].tasks && goals[0].tasks.length > 0 && (
									<div className='mb-3'>
										<p className='text-sm text-gray-600 dark:text-gray-400 mb-2'>
											Progress:
										</p>
										<div className='flex items-center space-x-2'>
											<div className='flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2'>
												<div
													className='bg-teal-500 h-2 rounded-full transition-all duration-300'
													style={{
														width: `${
															(goals[0].tasks.filter(
																(task) => task.status === 'Completed',
															).length /
																goals[0].tasks.length) *
															100
														}%`,
													}}></div>
											</div>
											<span className='text-sm text-gray-600 dark:text-gray-400'>
												{
													goals[0].tasks.filter(
														(task) => task.status === 'Completed',
													).length
												}
												/{goals[0].tasks.length} tasks
											</span>
										</div>
									</div>
								)}

								<span className='text-sm text-gray-500 dark:text-gray-400'>
									Created: {new Date(goals[0].createdAt).toLocaleDateString()}
								</span>
							</div>
						) : (
							<p className='text-gray-500 dark:text-gray-400'>
								No goals added yet
							</p>
						)}
					</div>
				</div>
			</div>
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Profile;
