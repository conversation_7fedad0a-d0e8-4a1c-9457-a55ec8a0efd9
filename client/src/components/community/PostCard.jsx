import React, { useState } from 'react';
import {
	Bookmark,
	Heart,
	MessageSquare,
	MoreHorizontal,
	Send,
} from 'lucide-react';
import {
	useToggleLike as useToggleGoalLike,
	useToggleBookmark as useToggleGoalBookmark,
	useAddComment as useAddGoalComment,
} from '@/src/lib/hooks/useGoal';
import {
	useToggleLike as useToggleValueLike,
	useToggleBookmark as useToggleValueBookmark,
	useAddComment as useAddValueComment,
} from '@/src/lib/hooks/useValues';
import {
	useToggleLike as useToggleMissionLike,
	useToggleBookmark as useToggleMissionBookmark,
	useAddComment as useAddMissionComment,
} from '@/src/lib/hooks/useLifeMissions';
import toast from 'react-hot-toast';
import { CustomImage } from '../common';
import Image from 'next/image';

const PostCard = ({
	post,
	onCommentClick,
	currentUserId,
	userProfile,
	updatePostLike,
	updatePostBookmark,
	updatePostComments,
}) => {
	const [showCommentInput, setShowCommentInput] = useState(false);
	const [commentText, setCommentText] = useState('');
	const [isSubmittingComment, setIsSubmittingComment] = useState(false);

	// Get the appropriate hooks based on post type
	const getHooks = () => {
		switch (post.type) {
			case 'goal':
				return {
					useToggleLike: useToggleGoalLike,
					useToggleBookmark: useToggleGoalBookmark,
					useAddComment: useAddGoalComment,
				};
			case 'value':
				return {
					useToggleLike: useToggleValueLike,
					useToggleBookmark: useToggleValueBookmark,
					useAddComment: useAddValueComment,
				};
			case 'lifeMission':
				return {
					useToggleLike: useToggleMissionLike,
					useToggleBookmark: useToggleMissionBookmark,
					useAddComment: useAddMissionComment,
				};
			default:
				return {
					useToggleLike: useToggleGoalLike,
					useToggleBookmark: useToggleGoalBookmark,
					useAddComment: useAddGoalComment,
				};
		}
	};

	const { useToggleLike, useToggleBookmark, useAddComment } = getHooks();

	// Initialize mutations
	const toggleLikeMutation = useToggleLike();
	const toggleBookmarkMutation = useToggleBookmark();
	const addCommentMutation = useAddComment();

	// Handle like toggle with optimistic updates
	const handleLike = async () => {
		const newLikedState = !post.isLiked;

		// Optimistic update
		updatePostLike(post.id, newLikedState);

		try {
			await toggleLikeMutation.mutateAsync(post.originalId);
			// Success feedback
			// toast.success(newLikedState ? 'Liked!' : 'Removed like');
		} catch (error) {
			// Revert optimistic update on error
			updatePostLike(post.id, post.isLiked);
			toast.error('Failed to update like');
			console.error('Error toggling like:', error);
		}
	};

	// Handle bookmark toggle with optimistic updates
	const handleBookmark = async () => {
		const newBookmarkedState = !post.isBookmarked;

		// Optimistic update
		updatePostBookmark(post.id, newBookmarkedState);

		try {
			await toggleBookmarkMutation.mutateAsync(post.originalId);
			// Success feedback
			toast.success(newBookmarkedState ? 'Bookmarked!' : 'Removed bookmark');
		} catch (error) {
			// Revert optimistic update on error
			updatePostBookmark(post.id, post.isBookmarked);
			toast.error('Failed to update bookmark');
			console.error('Error toggling bookmark:', error);
		}
	};

	// Handle comment submission with optimistic updates
	const handleCommentSubmit = async (e) => {
		e.preventDefault();
		if (!commentText.trim()) return;

		setIsSubmittingComment(true);

		// Optimistic update - increment comment count
		updatePostComments(post.id, true);

		try {
			await addCommentMutation.mutateAsync({
				id: post.originalId,
				text: commentText.trim(),
			});
			setCommentText('');
			setShowCommentInput(false);
			toast.success('Comment added!');
		} catch (error) {
			// Revert optimistic update on error
			updatePostComments(post.id, false);
			toast.error('Failed to add comment');
			console.error('Error adding comment:', error);
		} finally {
			setIsSubmittingComment(false);
		}
	};

	// Handle comment button click
	const handleCommentClick = () => {
		if (onCommentClick) {
			onCommentClick(post);
		} else {
			setShowCommentInput(!showCommentInput);
		}
	};

	// Format time ago
	const formatTimeAgo = (dateString) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInSeconds = Math.floor((now - date) / 1000);

		if (diffInSeconds < 60) return 'just now';
		if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
		if (diffInSeconds < 86400)
			return `${Math.floor(diffInSeconds / 3600)}h ago`;
		if (diffInSeconds < 604800)
			return `${Math.floor(diffInSeconds / 86400)}d ago`;
		return date.toLocaleDateString();
	};
	console.log('Card', post);
	console.log('USerprofile from card', userProfile);

	return (
		<div className='bg-white border-b border-gray-200 py-4 mb-4 rounded-md shadow-sm'>
			{/* Header */}
			<div className='flex items-start space-x-3 px-4 mb-3'>
				<div className='w-12 h-12 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0'>
					<Image
						src={
							post.author.avatar
								? post.author.avatar
								: `https://ui-avatars.com/api/?name=${encodeURIComponent(
										post.author.name,
								  )}&background=random`
						}
						alt={post.author.name}
						width={48}
						height={48}
						className='rounded-full object-cover h-12 w-12 shadow-2xl border border-gray-400'
					/>
				</div>
				<div className='flex-1'>
					<div className='flex items-center justify-between'>
						<div>
							<span className='font-semibold text-black'>
								{post.author.name}
							</span>
							<span className='text-gray-500 text-sm ml-2'>
								shared {post.postTypeLabel}
							</span>
							{post.createdAt && (
								<span className='text-gray-400 text-sm ml-2'>
									• {formatTimeAgo(post.createdAt)}
								</span>
							)}
						</div>
						<button className='text-gray-400 hover:text-gray-600 p-1'>
							<MoreHorizontal className='w-5 h-5' />
						</button>
					</div>
				</div>
			</div>

			{/* Content */}
			<div className='px-4 mb-4'>
				<div className='bg-teal-50 border-2 border-teal-200 rounded-xl p-4'>
					<p className='text-gray-800 font-medium mb-2'>{post.content}</p>
					{post.description && (
						<p className='text-gray-600 text-sm'>{post.description}</p>
					)}
				</div>
			</div>

			{/* Actions */}
			<div className='flex items-center justify-between px-4'>
				<div className='flex items-center space-x-6'>
					<button
						onClick={handleLike}
						disabled={toggleLikeMutation.isLoading}
						className={`flex items-center space-x-2 transition-colors ${
							post.isLiked ? 'text-red-500' : 'text-gray-600 hover:text-red-500'
						} disabled:opacity-50`}>
						<Heart
							className={`w-5 h-5 ${post.isLiked ? 'fill-current' : ''}`}
						/>
						<span className='text-sm'>{post.likes}</span>
					</button>

					<button
						onClick={handleCommentClick}
						className='flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors'>
						<MessageSquare className='w-5 h-5' />
						<span className='text-sm'>{post.comments}</span>
					</button>
				</div>

				<button
					onClick={handleBookmark}
					disabled={toggleBookmarkMutation.isLoading}
					className={`transition-colors ${
						post.isBookmarked
							? 'text-yellow-500'
							: 'text-gray-600 hover:text-yellow-500'
					} disabled:opacity-50`}>
					<Bookmark
						className={`w-5 h-5 ${post.isBookmarked ? 'fill-current' : ''}`}
					/>
				</button>
			</div>

			{/* Comment Input */}
			{showCommentInput && (
				<div className='px-4 mt-4 pt-4 border-t border-gray-100'>
					<form
						onSubmit={handleCommentSubmit}
						className='flex space-x-3'>
						<div className='w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex-shrink-0'>
							<Image
								src={
									userProfile.avatar
										? post.author.avatar
										: `https://ui-avatars.com/api/?name=${encodeURIComponent(
												userProfile.name,
										  )}&background=random`
								}
								alt={post.author.name}
								width={48}
								height={48}
								className='rounded-full object-cover h-12 w-12 shadow-2xl border border-gray-400'
							/>
						</div>
						<div className='flex-1 flex space-x-2'>
							<input
								type='text'
								value={commentText}
								onChange={(e) => setCommentText(e.target.value)}
								placeholder='Write a comment...'
								className='flex-1 px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent'
								disabled={isSubmittingComment}
							/>
							<button
								type='submit'
								disabled={!commentText.trim() || isSubmittingComment}
								className='px-4 py-2 bg-teal-600 text-white rounded-full hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'>
								{isSubmittingComment ? (
									<div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
								) : (
									<Send className='w-4 h-4' />
								)}
							</button>
						</div>
					</form>
				</div>
			)}
		</div>
	);
};

export default PostCard;
