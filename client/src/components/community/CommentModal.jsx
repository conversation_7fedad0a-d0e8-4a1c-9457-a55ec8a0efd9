import React, { useState, useEffect } from 'react';
import { X, Send, Trash2, Heart, MessageSquare } from 'lucide-react';
import { useUserProfile } from '@/src/lib/hooks/useUser';
import { useGoalById } from '@/src/lib/hooks/useGoal';
import { useValueById } from '@/src/lib/hooks/useValues';
import { useLifeMissionById } from '@/src/lib/hooks/useLifeMissions';
import {
	useAddComment as useAddGoalComment,
	useDeleteComment as useDeleteGoalComment,
} from '@/src/lib/hooks/useGoal';
import {
	useAddComment as useAddValueComment,
	useDeleteComment as useDeleteValueComment,
} from '@/src/lib/hooks/useValues';
import {
	useAddComment as useAddMissionComment,
	useDeleteComment as useDeleteMissionComment,
} from '@/src/lib/hooks/useLifeMissions';
import toast from 'react-hot-toast';

const CommentModal = ({ post, isOpen, onClose }) => {
	const [commentText, setCommentText] = useState('');
	const [isSubmittingComment, setIsSubmittingComment] = useState(false);

	// Get current user profile
	const { data: userProfile } = useUserProfile();
	const currentUser =
		userProfile?.data?.user ||
		userProfile?.data ||
		userProfile?.user ||
		userProfile;

	console.log(currentUser);
	console.log('Post', post);

	// Get the appropriate hooks based on post type
	const getDetailHook = () => {
		switch (post?.type) {
			case 'goal':
				return useGoalById;
			case 'value':
				return useValueById;
			case 'lifeMission':
				return useLifeMissionById;
			default:
				return useGoalById;
		}
	};

	const getCommentHooks = () => {
		switch (post?.type) {
			case 'goal':
				return {
					useAddComment: useAddGoalComment,
					useDeleteComment: useDeleteGoalComment,
				};
			case 'value':
				return {
					useAddComment: useAddValueComment,
					useDeleteComment: useDeleteValueComment,
				};
			case 'lifeMission':
				return {
					useAddComment: useAddMissionComment,
					useDeleteComment: useDeleteMissionComment,
				};
			default:
				return {
					useAddComment: useAddGoalComment,
					useDeleteComment: useDeleteGoalComment,
				};
		}
	};

	const useDetailHook = getDetailHook();
	const { useAddComment, useDeleteComment } = getCommentHooks();

	// Fetch detailed post data with comments
	const { data: detailData, isLoading: isLoadingDetail } = useDetailHook(
		post?.originalId,
	);

	// Initialize mutations
	const addCommentMutation = useAddComment();
	const deleteCommentMutation = useDeleteComment();

	// Get comments from the detailed data
	const comments = detailData?.data?.comments || detailData?.comments || [];
	console.log(comments);

	// Handle comment submission
	const handleCommentSubmit = async (e) => {
		e.preventDefault();
		if (!commentText.trim()) return;

		setIsSubmittingComment(true);
		try {
			await addCommentMutation.mutateAsync({
				id: post.originalId,
				text: commentText.trim(),
			});
			setCommentText('');
			toast.success('Comment added!');
		} catch (error) {
			toast.error('Failed to add comment');
			console.error('Error adding comment:', error);
		} finally {
			setIsSubmittingComment(false);
		}
	};

	// Handle comment deletion
	const handleDeleteComment = async (commentId) => {
		if (!window.confirm('Are you sure you want to delete this comment?'))
			return;

		try {
			await deleteCommentMutation.mutateAsync({
				id: post.originalId,
				commentId,
			});
			toast.success('Comment deleted!');
		} catch (error) {
			toast.error('Failed to delete comment');
			console.error('Error deleting comment:', error);
		}
	};

	// Format time ago
	const formatTimeAgo = (dateString) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInSeconds = Math.floor((now - date) / 1000);

		if (diffInSeconds < 60) return 'just now';
		if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
		if (diffInSeconds < 86400)
			return `${Math.floor(diffInSeconds / 3600)}h ago`;
		if (diffInSeconds < 604800)
			return `${Math.floor(diffInSeconds / 86400)}d ago`;
		return date.toLocaleDateString();
	};

	// Close modal on escape key
	useEffect(() => {
		const handleEscape = (e) => {
			if (e.key === 'Escape') onClose();
		};

		if (isOpen) {
			document.addEventListener('keydown', handleEscape);
			document.body.style.overflow = 'hidden';
		}

		return () => {
			document.removeEventListener('keydown', handleEscape);
			document.body.style.overflow = 'unset';
		};
	}, [isOpen, onClose]);

	if (!isOpen || !post) return null;

	return (
		<div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
			<div className='bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col shadow-2xl'>
				{/* Header */}
				<div className='flex items-center justify-between p-6 border-b border-gray-200'>
					<div className='flex items-center space-x-3'>
						<MessageSquare className='w-6 h-6 text-teal-600' />
						<h2 className='text-xl font-semibold text-gray-900'>Comments</h2>
						<span className='bg-gray-100 text-gray-600 text-sm px-2 py-1 rounded-full font-medium'>
							{comments.length}
						</span>
					</div>
					<button
						onClick={onClose}
						className='text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-colors'>
						<X className='w-6 h-6' />
					</button>
				</div>

				{/* Post Preview */}
				<div className='p-6 border-b border-gray-100 bg-gray-50'>
					<div className='flex items-start space-x-4'>
						<div className='w-12 h-12 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0 ring-2 ring-white shadow-sm'>
							<img
								src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
									post.author.name,
								)}&background=random`}
								alt={post.author.name}
								className='w-full h-full object-cover'
							/>
						</div>
						<div className='flex-1 min-w-0'>
							<div className='flex items-center space-x-2 mb-2'>
								<span className='font-semibold text-gray-900'>
									{post.author.name}
								</span>
								<span className='text-gray-500 text-sm'>
									shared {post.postTypeLabel}
								</span>
							</div>
							<div className='bg-teal-50 border border-teal-200 rounded-lg p-3'>
								<p className='text-gray-900 font-medium mb-1'>{post.content}</p>
								{post.description && (
									<p className='text-gray-700 text-sm'>{post.description}</p>
								)}
							</div>
						</div>
					</div>
				</div>

				{/* Comments List */}
				<div className='flex-1 overflow-y-auto'>
					{isLoadingDetail ? (
						<div className='flex items-center justify-center py-12'>
							<div className='flex items-center space-x-3'>
								<div className='w-6 h-6 border-2 border-teal-600 border-t-transparent rounded-full animate-spin' />
								<span className='text-gray-600 font-medium'>
									Loading comments...
								</span>
							</div>
						</div>
					) : comments.length > 0 ? (
						<div className='p-6 space-y-6'>
							{comments.map((comment, index) => (
								<div
									key={comment._id}
									className={`flex items-start space-x-4 ${
										index !== comments.length - 1
											? 'pb-6 border-b border-gray-100'
											: ''
									}`}>
									<div className='w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0 ring-2 ring-white shadow-sm'>
										<img
											src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
												comment.userId?.name || 'Anonymous',
											)}&background=random`}
											alt={comment.userId?.name || 'Anonymous'}
											className='w-full h-full object-cover'
										/>
									</div>
									<div className='flex-1 min-w-0'>
										<div className='bg-gray-100 rounded-2xl px-4 py-3'>
											<div className='flex items-center justify-between mb-2'>
												<span className='font-semibold text-sm text-gray-900'>
													{comment.userId?.name || 'Anonymous'}
												</span>
												{comment.userId?._id === currentUser?._id && (
													<button
														onClick={() => handleDeleteComment(comment._id)}
														className='text-gray-400 hover:text-red-500 p-1 rounded-full hover:bg-red-50 transition-colors'
														disabled={deleteCommentMutation.isLoading}
														title='Delete comment'>
														<Trash2 className='w-4 h-4' />
													</button>
												)}
											</div>
											<p className='text-gray-800 leading-relaxed'>
												{comment.text}
											</p>
										</div>
										<div className='flex items-center space-x-4 mt-2 ml-4'>
											<time className='text-xs text-gray-500 font-medium'>
												{formatTimeAgo(comment.createdAt)}
											</time>
										</div>
									</div>
								</div>
							))}
						</div>
					) : (
						<div className='flex flex-col items-center justify-center py-16 text-center'>
							<div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4'>
								<MessageSquare className='w-8 h-8 text-gray-400' />
							</div>
							<h3 className='text-lg font-semibold text-gray-900 mb-2'>
								No comments yet
							</h3>
							<p className='text-gray-500 max-w-sm leading-relaxed'>
								Be the first to share your thoughts and start a meaningful
								conversation!
							</p>
						</div>
					)}
				</div>

				{/* Comment Input */}
				<div className='p-6 border-t border-gray-200 bg-gray-50'>
					<form
						onSubmit={handleCommentSubmit}
						className='flex items-start space-x-4'>
						<div className='w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex-shrink-0 overflow-hidden ring-2 ring-white shadow-sm'>
							<img
								src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
									currentUser?.name || 'You',
								)}&background=random`}
								alt={currentUser?.name || 'You'}
								className='w-full h-full object-cover'
							/>
						</div>
						<div className='flex-1 flex flex-col space-y-3'>
							<div className='flex items-end space-x-3'>
								<div className='flex-1'>
									<textarea
										value={commentText}
										onChange={(e) => setCommentText(e.target.value)}
										placeholder='Write a thoughtful comment...'
										rows={3}
										className='w-full text-black px-4 py-3 border border-gray-300 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-200'
										disabled={isSubmittingComment}
										onKeyDown={(e) => {
											if (e.key === 'Enter' && !e.shiftKey) {
												e.preventDefault();
												handleCommentSubmit(e);
											}
										}}
									/>
								</div>
								<button
									type='submit'
									disabled={!commentText.trim() || isSubmittingComment}
									className='px-6 py-3 bg-teal-600 text-white rounded-xl hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md'>
									{isSubmittingComment ? (
										<div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin' />
									) : (
										<Send className='w-5 h-5' />
									)}
								</button>
							</div>
							<p className='text-xs text-gray-500 ml-1'>
								Press Enter to post, Shift+Enter for new line
							</p>
						</div>
					</form>
				</div>
			</div>
		</div>
	);
};

export default CommentModal;
