const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
	userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
	text: { type: String, required: true },
	createdAt: { type: Date, default: Date.now },
});
const valueSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		name: { type: String, required: true },
		definition: { type: String, required: true },
		isPublic: { type: Boolean, default: false },
		likes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
		bookmarks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
		comments: [commentSchema],
	},
	{ timestamps: true },
);

module.exports = mongoose.model('Value', valueSchema);
