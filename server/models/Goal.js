const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
	userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
	text: { type: String, required: true },
	createdAt: { type: Date, default: Date.now },
});

const goalSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		title: { type: String, required: true },
		tasks: [
			{
				name: { type: String, required: true },
				status: {
					type: String,
					enum: ['Not Started', 'In Progress', 'Completed'],
					default: 'Not Started',
				},
				category: { type: String, enum: ['red', 'amber', 'green'] },
				originalCategory: { type: String, enum: ['red', 'amber', 'green'] },
			},
		],
		isPublic: { type: Boolean, default: false },
		likes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
		bookmarks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
		comments: [commentSchema],
	},
	{ timestamps: true },
);

module.exports = mongoose.model('Goal', goalSchema);
